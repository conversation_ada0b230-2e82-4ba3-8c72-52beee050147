"""
基于多任务自监督学习的股票涨跌预测方法
参考专利：CN113159945A

该方法主要包含以下核心组件：
1. 基于Transformer的股票技术数据序列编码器
2. 多个自监督辅助任务
3. 注意力机制
4. 股票涨跌预测模型
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import pandas as pd
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
import math
import warnings
warnings.filterwarnings('ignore')


class PositionalEncoding(nn.Module):
    """位置编码模块"""
    def __init__(self, d_model, max_len=5000):
        super(PositionalEncoding, self).__init__()

        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() *
                           (-math.log(10000.0) / d_model))

        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)

        self.register_buffer('pe', pe)

    def forward(self, x):
        # x shape: (batch_size, seq_len, d_model)
        seq_len = x.size(1)
        return x + self.pe[:seq_len, :].unsqueeze(0)


class SimpleAttention(nn.Module):
    """简化的注意力机制"""
    def __init__(self, d_model):
        super(SimpleAttention, self).__init__()
        self.d_model = d_model
        self.attention = nn.MultiheadAttention(d_model, num_heads=4, batch_first=True)

    def forward(self, query, key, value, mask=None):
        attn_output, _ = self.attention(query, key, value, attn_mask=mask)
        return attn_output


class TransformerBlock(nn.Module):
    """Transformer块"""
    def __init__(self, d_model, n_heads, d_ff, dropout=0.1):
        super(TransformerBlock, self).__init__()
        self.attention = SimpleAttention(d_model)
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        
        self.feed_forward = nn.Sequential(
            nn.Linear(d_model, d_ff),
            nn.ReLU(),
            nn.Linear(d_ff, d_model)
        )
        
        self.dropout = nn.Dropout(dropout)
    
    def forward(self, x, mask=None):
        # 自注意力
        attn_output = self.attention(x, x, x, mask)
        x = self.norm1(x + self.dropout(attn_output))
        
        # 前馈网络
        ff_output = self.feed_forward(x)
        x = self.norm2(x + self.dropout(ff_output))
        
        return x


class StockSequenceEncoder(nn.Module):
    """基于Transformer的股票技术数据序列编码器"""
    def __init__(self, input_dim, d_model=256, n_heads=8, n_layers=6, d_ff=1024, dropout=0.1):
        super(StockSequenceEncoder, self).__init__()
        self.d_model = d_model
        
        # 输入投影层
        self.input_projection = nn.Linear(input_dim, d_model)
        
        # 位置编码
        self.pos_encoding = PositionalEncoding(d_model)
        
        # Transformer层
        self.transformer_blocks = nn.ModuleList([
            TransformerBlock(d_model, n_heads, d_ff, dropout)
            for _ in range(n_layers)
        ])
        
        self.dropout = nn.Dropout(dropout)
    
    def forward(self, x, mask=None):
        # x shape: (batch_size, seq_len, input_dim)
        x = self.input_projection(x) * math.sqrt(self.d_model)
        x = self.pos_encoding(x)
        x = self.dropout(x)
        
        for transformer in self.transformer_blocks:
            x = transformer(x, mask)
        
        return x


class SelfSupervisedTasks(nn.Module):
    """多个自监督辅助任务 - 根据专利CN113159945A实现"""
    def __init__(self, d_model):
        super(SelfSupervisedTasks, self).__init__()
        self.d_model = d_model

        # 任务1: 正负样本判别任务
        self.positive_negative_classifier = nn.Sequential(
            nn.Linear(d_model * 2, d_model),
            nn.ReLU(),
            nn.Linear(d_model, 2)  # 二分类：正样本或负样本
        )

        # 任务2: 价格变化同向性任务
        self.price_direction_classifier = nn.Sequential(
            nn.Linear(d_model * 2, d_model),
            nn.ReLU(),
            nn.Linear(d_model, 2)  # 二分类：同向或异向
        )

        # 任务3: 成交量变化同向性任务
        self.volume_direction_classifier = nn.Sequential(
            nn.Linear(d_model * 2, d_model),
            nn.ReLU(),
            nn.Linear(d_model, 2)  # 二分类：同向或异向
        )
    
    def forward(self, anchor_repr, sample_repr, task_labels):
        """
        根据专利CN113159945A实现的三个自监督任务

        Args:
            anchor_repr: 锚序列表征 (batch_size, d_model)
            sample_repr: 样本序列表征 (batch_size, d_model)
            task_labels: 包含三个任务标签的字典
                - 'positive_negative': 正负样本标签
                - 'price_direction': 价格变化同向性标签
                - 'volume_direction': 成交量变化同向性标签
        """
        # 合并锚序列和样本序列的表征
        combined_repr = torch.cat([anchor_repr, sample_repr], dim=-1)

        losses = {}

        # 任务1: 正负样本判别任务
        if 'positive_negative' in task_labels:
            pos_neg_logits = self.positive_negative_classifier(combined_repr)
            pos_neg_loss = F.cross_entropy(pos_neg_logits, task_labels['positive_negative'])
            losses['positive_negative_loss'] = pos_neg_loss

        # 任务2: 价格变化同向性任务
        if 'price_direction' in task_labels:
            price_dir_logits = self.price_direction_classifier(combined_repr)
            price_dir_loss = F.cross_entropy(price_dir_logits, task_labels['price_direction'])
            losses['price_direction_loss'] = price_dir_loss

        # 任务3: 成交量变化同向性任务
        if 'volume_direction' in task_labels:
            volume_dir_logits = self.volume_direction_classifier(combined_repr)
            volume_dir_loss = F.cross_entropy(volume_dir_logits, task_labels['volume_direction'])
            losses['volume_direction_loss'] = volume_dir_loss

        return losses


class LSTMStockPredictor(nn.Module):
    """基于LSTM的股票涨跌预测模块 - 根据专利CN113159945A实现"""
    def __init__(self, d_model, hidden_size=128, num_layers=2, dropout=0.1):
        super(LSTMStockPredictor, self).__init__()

        # LSTM层
        self.lstm = nn.LSTM(
            input_size=d_model,
            hidden_size=hidden_size,
            num_layers=num_layers,
            dropout=dropout,
            batch_first=True
        )

        # 前馈神经网络进行二分类
        self.classifier = nn.Sequential(
            nn.Linear(hidden_size, hidden_size // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_size // 2, 2)  # 二分类：涨或跌
        )

    def forward(self, sequence_embeddings):
        """
        Args:
            sequence_embeddings: (batch_size, seq_len, d_model) 序列表征
        Returns:
            prediction: (batch_size, 2) 涨跌概率
        """
        # 通过LSTM编码
        lstm_out, (hidden, cell) = self.lstm(sequence_embeddings)

        # 取最后一个时间步的隐层表征
        last_hidden = lstm_out[:, -1, :]  # (batch_size, hidden_size)

        # 通过分类器预测
        prediction = self.classifier(last_hidden)

        return prediction


class MultiTaskStockPredictor(nn.Module):
    """多任务自监督学习股票预测模型 - 根据专利CN113159945A实现"""
    def __init__(self, input_dim, d_model=256, n_heads=8, n_layers=6,
                 d_ff=1024, seq_len=60, dropout=0.1):
        super(MultiTaskStockPredictor, self).__init__()

        # 编码器
        self.encoder = StockSequenceEncoder(
            input_dim, d_model, n_heads, n_layers, d_ff, dropout
        )

        # 自监督任务
        self.self_supervised_tasks = SelfSupervisedTasks(d_model)

        # 注意力机制层，用于获取序列的总表征
        self.attention_pooling = nn.MultiheadAttention(d_model, num_heads=4, batch_first=True)
        self.attention_query = nn.Parameter(torch.randn(1, 1, d_model))

    def forward(self, anchor_seq, sample_seq=None, task_labels=None, mode='train'):
        if mode == 'train' and sample_seq is not None:
            # 训练模式：自监督学习
            # 编码锚序列和样本序列
            anchor_encoded = self.encoder(anchor_seq)  # (batch_size, seq_len, d_model)
            sample_encoded = self.encoder(sample_seq)  # (batch_size, seq_len, d_model)

            # 使用注意力机制获取序列的总表征
            batch_size = anchor_encoded.size(0)
            query = self.attention_query.expand(batch_size, -1, -1)

            anchor_repr, _ = self.attention_pooling(query, anchor_encoded, anchor_encoded)
            sample_repr, _ = self.attention_pooling(query, sample_encoded, sample_encoded)

            anchor_repr = anchor_repr.squeeze(1)  # (batch_size, d_model)
            sample_repr = sample_repr.squeeze(1)  # (batch_size, d_model)

            # 计算自监督任务损失
            ssl_losses = self.self_supervised_tasks(anchor_repr, sample_repr, task_labels)

            return {
                'ssl_losses': ssl_losses,
                'anchor_repr': anchor_repr,
                'sample_repr': sample_repr
            }
        else:
            # 推理模式：只进行编码
            encoded_seq = self.encoder(anchor_seq)
            return encoded_seq


class StockDataset(Dataset):
    """股票数据集 - 根据专利CN113159945A实现锚-样本序列对采样"""
    def __init__(self, data, seq_len=30, mode='pretrain'):
        """
        Args:
            data: 股票数据 (n_samples, seq_len, n_features)
            seq_len: 序列长度
            mode: 'pretrain' 用于自监督预训练, 'finetune' 用于下游任务
        """
        self.data = data
        self.seq_len = seq_len
        self.mode = mode
        self.n_samples, self.total_seq_len, self.n_features = data.shape

        if mode == 'pretrain':
            # 为自监督学习准备锚-样本序列对
            self.anchor_sample_pairs = self._create_anchor_sample_pairs()

    def _create_anchor_sample_pairs(self):
        """创建锚-样本序列对"""
        pairs = []

        for i in range(self.n_samples):
            stock_data = self.data[i]  # (total_seq_len, n_features)

            # 确保有足够的数据创建锚序列和样本序列
            if self.total_seq_len < self.seq_len * 2:
                continue

            # 创建锚序列 (前seq_len个时间步)
            anchor_start = 0
            anchor_end = self.seq_len
            anchor_seq = stock_data[anchor_start:anchor_end]

            # 正样本：紧跟着锚序列的下一段序列
            positive_start = anchor_end
            positive_end = anchor_end + self.seq_len
            if positive_end <= self.total_seq_len:
                positive_seq = stock_data[positive_start:positive_end]

                # 计算价格和成交量变化标签
                price_label = self._get_price_direction_label(anchor_seq, positive_seq)
                volume_label = self._get_volume_direction_label(anchor_seq, positive_seq)

                pairs.append({
                    'anchor': anchor_seq,
                    'sample': positive_seq,
                    'positive_negative': 1,  # 正样本
                    'price_direction': price_label,
                    'volume_direction': volume_label,
                    'stock_idx': i
                })

            # 时序负样本：同一股票的远距离序列
            if self.total_seq_len >= self.seq_len * 3:
                negative_start = self.seq_len * 2
                negative_end = negative_start + self.seq_len
                if negative_end <= self.total_seq_len:
                    negative_seq = stock_data[negative_start:negative_end]

                    price_label = self._get_price_direction_label(anchor_seq, negative_seq)
                    volume_label = self._get_volume_direction_label(anchor_seq, negative_seq)

                    pairs.append({
                        'anchor': anchor_seq,
                        'sample': negative_seq,
                        'positive_negative': 0,  # 负样本
                        'price_direction': price_label,
                        'volume_direction': volume_label,
                        'stock_idx': i
                    })

            # 对比负样本：其他股票的同时期序列
            if i < self.n_samples - 1:
                other_stock_idx = (i + 1) % self.n_samples
                other_stock_data = self.data[other_stock_idx]
                if other_stock_data.shape[0] >= anchor_end:
                    other_seq = other_stock_data[anchor_start:anchor_end]

                    price_label = self._get_price_direction_label(anchor_seq, other_seq)
                    volume_label = self._get_volume_direction_label(anchor_seq, other_seq)

                    pairs.append({
                        'anchor': anchor_seq,
                        'sample': other_seq,
                        'positive_negative': 0,  # 负样本
                        'price_direction': price_label,
                        'volume_direction': volume_label,
                        'stock_idx': i
                    })

        return pairs

    def _get_price_direction_label(self, anchor_seq, sample_seq):
        """计算价格变化同向性标签"""
        # 使用收盘价 (假设第4列是收盘价)
        anchor_price_change = anchor_seq[-1, 3] - anchor_seq[-2, 3] if len(anchor_seq) > 1 else 0
        sample_price_change = sample_seq[-1, 3] - sample_seq[-2, 3] if len(sample_seq) > 1 else 0

        # 同向为1，异向为0
        return 1 if (anchor_price_change * sample_price_change) >= 0 else 0

    def _get_volume_direction_label(self, anchor_seq, sample_seq):
        """计算成交量变化同向性标签"""
        # 使用成交量 (假设第5列是成交量)
        anchor_volume_change = anchor_seq[-1, 4] - anchor_seq[-2, 4] if len(anchor_seq) > 1 else 0
        sample_volume_change = sample_seq[-1, 4] - sample_seq[-2, 4] if len(sample_seq) > 1 else 0

        # 同向为1，异向为0
        return 1 if (anchor_volume_change * sample_volume_change) >= 0 else 0

    def __len__(self):
        if self.mode == 'pretrain':
            return len(self.anchor_sample_pairs)
        else:
            return self.n_samples

    def __getitem__(self, idx):
        if self.mode == 'pretrain':
            pair = self.anchor_sample_pairs[idx]
            return {
                'anchor': torch.FloatTensor(pair['anchor']),
                'sample': torch.FloatTensor(pair['sample']),
                'labels': {
                    'positive_negative': torch.LongTensor([pair['positive_negative']]),
                    'price_direction': torch.LongTensor([pair['price_direction']]),
                    'volume_direction': torch.LongTensor([pair['volume_direction']])
                }
            }
        else:
            # 用于下游任务的数据
            sequence = self.data[idx]
            return torch.FloatTensor(sequence)


def create_sample_data(n_samples=10000, seq_len=60, n_features=5):
    """创建示例股票数据"""
    # 模拟股票技术指标数据
    np.random.seed(42)
    
    # 生成基础价格序列
    prices = []
    current_price = 100.0
    
    for _ in range(n_samples + seq_len):
        # 随机游走模拟价格变化
        change = np.random.normal(0, 0.02)
        current_price *= (1 + change)
        prices.append(current_price)
    
    prices = np.array(prices)
    
    # 计算技术指标
    data = []
    labels = []
    
    for i in range(len(prices) - seq_len):
        # 特征：开盘价、最高价、最低价、收盘价、成交量
        price_window = prices[i:i + seq_len + 1]
        
        # 模拟OHLCV数据
        features = []
        for j in range(seq_len):
            open_price = price_window[j]
            close_price = price_window[j + 1]
            high_price = max(open_price, close_price) * (1 + np.random.uniform(0, 0.01))
            low_price = min(open_price, close_price) * (1 - np.random.uniform(0, 0.01))
            volume = np.random.uniform(1000, 10000)
            
            features.append([open_price, high_price, low_price, close_price, volume])
        
        data.append(features)
        
        # 标签：下一天的涨跌情况（二分类：涨/跌）
        next_price = price_window[-1]
        current_price = price_window[-2]

        if next_price > current_price:  # 涨
            label = 1  # 涨
        else:  # 跌
            label = 0  # 跌
        
        labels.append(label)
    
    return np.array(data), np.array(labels)


def train_model():
    """训练模型"""
    # 创建数据
    print("创建示例数据...")
    data, labels = create_sample_data(n_samples=1000, seq_len=30, n_features=5)
    
    # 数据标准化
    scaler = StandardScaler()
    data_reshaped = data.reshape(-1, data.shape[-1])
    data_scaled = scaler.fit_transform(data_reshaped)
    data_scaled = data_scaled.reshape(data.shape)
    
    # 划分训练集和测试集
    X_train, X_test, y_train, y_test = train_test_split(
        data_scaled, labels, test_size=0.2, random_state=42
    )
    
    # 创建数据集和数据加载器 - 暂时使用简单的数据集
    from torch.utils.data import TensorDataset
    train_dataset = TensorDataset(torch.FloatTensor(X_train), torch.LongTensor(y_train))
    test_dataset = TensorDataset(torch.FloatTensor(X_test), torch.LongTensor(y_test))

    train_loader = DataLoader(train_dataset, batch_size=8, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=8, shuffle=False)
    
    # 创建模型
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = MultiTaskStockPredictor(
        input_dim=5,  # OHLCV
        d_model=128,
        n_heads=4,
        n_layers=3,
        seq_len=30,
        num_classes=3
    ).to(device)
    
    # 优化器和损失函数
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
    criterion = nn.CrossEntropyLoss()
    
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    print(f"使用设备: {device}")
    
    # 训练循环
    model.train()
    num_epochs = 10
    
    for epoch in range(num_epochs):
        total_loss = 0
        correct = 0
        total = 0
        
        for batch_idx, (data_batch, labels_batch) in enumerate(train_loader):
            data_batch = data_batch.to(device)
            labels_batch = labels_batch.squeeze().to(device)
            
            optimizer.zero_grad()
            
            # 前向传播
            outputs = model(data_batch, data_batch, mode='train')
            
            # 主任务损失（股票预测）
            main_loss = criterion(outputs['prediction'], labels_batch)
            
            # 自监督任务损失
            ssl_outputs = outputs['ssl_outputs']
            ssl_loss = (ssl_outputs['mlm_loss'] +
                       ssl_outputs['next_step_loss'] +
                       ssl_outputs['order_loss'])

            # 确保ssl_loss是标量
            if isinstance(ssl_loss, torch.Tensor) and ssl_loss.dim() > 0:
                ssl_loss = ssl_loss.mean()
            
            # 总损失
            total_loss_batch = main_loss + 0.1 * ssl_loss  # 自监督损失权重为0.1
            
            # 反向传播
            total_loss_batch.backward()
            optimizer.step()
            
            total_loss += total_loss_batch.item()
            
            # 计算准确率
            _, predicted = torch.max(outputs['prediction'].data, 1)
            total += labels_batch.size(0)
            correct += (predicted == labels_batch).sum().item()
            
            if batch_idx % 50 == 0:
                print(f'Epoch {epoch+1}/{num_epochs}, Batch {batch_idx}, '
                      f'Loss: {total_loss_batch.item():.4f}, '
                      f'Acc: {100.*correct/total:.2f}%')
        
        avg_loss = total_loss / len(train_loader)
        accuracy = 100. * correct / total
        print(f'Epoch {epoch+1}/{num_epochs} - Loss: {avg_loss:.4f}, Accuracy: {accuracy:.2f}%')
    
    # 测试模型
    model.eval()
    test_correct = 0
    test_total = 0
    
    with torch.no_grad():
        for data_batch, labels_batch in test_loader:
            data_batch = data_batch.to(device)
            labels_batch = labels_batch.squeeze().to(device)
            
            outputs = model(data_batch, mode='inference')
            _, predicted = torch.max(outputs.data, 1)
            
            test_total += labels_batch.size(0)
            test_correct += (predicted == labels_batch).sum().item()
    
    test_accuracy = 100. * test_correct / test_total
    print(f'测试准确率: {test_accuracy:.2f}%')
    
    # 保存模型
    torch.save(model.state_dict(), 'multi_task_stock_predictor.pth')
    print("模型已保存为 'multi_task_stock_predictor.pth'")
    
    return model, scaler


def pretrain_model():
    """预训练模型 - 根据专利CN113159945A实现"""
    # 创建数据
    print("创建示例数据...")
    data, _ = create_sample_data(n_samples=1000, seq_len=60, n_features=5)

    # 数据标准化
    scaler = StandardScaler()
    data_reshaped = data.reshape(-1, data.shape[-1])
    data_scaled = scaler.fit_transform(data_reshaped)
    data_scaled = data_scaled.reshape(data.shape)

    # 创建数据集和数据加载器
    dataset = StockDataset(data_scaled, seq_len=30, mode='pretrain')
    dataloader = DataLoader(dataset, batch_size=16, shuffle=True)

    # 创建模型
    model = MultiTaskStockPredictor(input_dim=5, d_model=128, n_heads=4, n_layers=3)
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")

    # 设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    model.to(device)

    # 优化器
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)

    # 预训练循环
    model.train()
    print("开始自监督预训练...")

    for epoch in range(5):  # 预训练5个epoch
        total_loss = 0

        for batch_idx, batch_data in enumerate(dataloader):
            anchor_seq = batch_data['anchor'].to(device)
            sample_seq = batch_data['sample'].to(device)

            # 准备标签
            task_labels = {}
            for task_name in ['positive_negative', 'price_direction', 'volume_direction']:
                task_labels[task_name] = batch_data['labels'][task_name].to(device).squeeze()

            optimizer.zero_grad()

            # 前向传播
            outputs = model(anchor_seq, sample_seq, task_labels, mode='train')
            ssl_losses = outputs['ssl_losses']

            # 计算总损失
            total_batch_loss = 0
            for loss_name, loss_value in ssl_losses.items():
                total_batch_loss += loss_value

            # 反向传播
            total_batch_loss.backward()
            optimizer.step()

            total_loss += total_batch_loss.item()

            if batch_idx % 50 == 0:
                print(f'Epoch {epoch}, Batch {batch_idx}, Loss: {total_batch_loss.item():.4f}')

        avg_loss = total_loss / len(dataloader)
        print(f'Epoch {epoch} 完成, 平均损失: {avg_loss:.4f}')

    return model, scaler


def finetune_model(pretrained_model, scaler):
    """微调模型进行股票涨跌预测 - 第二阶段"""
    print("\n开始微调模型...")

    # 创建LSTM预测器
    lstm_predictor = LSTMStockPredictor(d_model=128)

    # 创建微调数据
    data, labels = create_sample_data(n_samples=500, seq_len=30, n_features=5)

    # 数据标准化
    data_reshaped = data.reshape(-1, data.shape[-1])
    data_scaled = scaler.transform(data_reshaped)  # 使用预训练时的scaler
    data_scaled = data_scaled.reshape(data.shape)

    # 设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    pretrained_model.to(device)
    lstm_predictor.to(device)

    # 使用预训练模型提取特征
    pretrained_model.eval()

    # 准备微调数据
    sequences = []
    targets = []

    print("使用预训练模型提取特征...")
    with torch.no_grad():
        for i in range(len(data_scaled)):
            seq_tensor = torch.FloatTensor(data_scaled[i]).unsqueeze(0).to(device)
            encoded_seq = pretrained_model(seq_tensor, mode='inference')
            sequences.append(encoded_seq.cpu())
            targets.append(labels[i])

    sequences = torch.cat(sequences, dim=0)
    targets = torch.LongTensor(targets)

    # 创建微调数据集
    from torch.utils.data import TensorDataset
    finetune_dataset = TensorDataset(sequences, targets)
    finetune_dataloader = DataLoader(finetune_dataset, batch_size=16, shuffle=True)

    # 优化器和损失函数
    optimizer = torch.optim.Adam(lstm_predictor.parameters(), lr=0.0001)
    criterion = nn.CrossEntropyLoss()

    # 微调循环
    lstm_predictor.train()
    print("开始LSTM微调训练...")

    for epoch in range(5):
        total_loss = 0
        correct = 0
        total = 0

        for batch_idx, (seq_batch, label_batch) in enumerate(finetune_dataloader):
            seq_batch = seq_batch.to(device)
            label_batch = label_batch.to(device)

            optimizer.zero_grad()

            # 前向传播
            predictions = lstm_predictor(seq_batch)
            loss = criterion(predictions, label_batch)

            # 反向传播
            loss.backward()
            optimizer.step()

            total_loss += loss.item()

            # 计算准确率
            _, predicted = torch.max(predictions.data, 1)
            total += label_batch.size(0)
            correct += (predicted == label_batch).sum().item()

            if batch_idx % 10 == 0:
                print(f'微调 Epoch {epoch}, Batch {batch_idx}, '
                      f'Loss: {loss.item():.4f}, '
                      f'Acc: {100 * correct / total:.2f}%')

        print(f'微调 Epoch {epoch} 完成, 平均损失: {total_loss/len(finetune_dataloader):.4f}, '
              f'准确率: {100 * correct / total:.2f}%')

    return lstm_predictor


if __name__ == "__main__":
    print("基于多任务自监督学习的股票涨跌预测方法")
    print("=" * 50)

    # 第一阶段：自监督预训练
    print("第一阶段：自监督预训练")
    pretrained_model, scaler = pretrain_model()

    print("\n预训练完成！")
    print("该模型实现了专利CN113159945A中的以下功能：")
    print("1. 基于Transformer的股票技术数据序列编码器")
    print("2. 三个自监督辅助任务：")
    print("   - 正负样本判别任务")
    print("   - 价格变化同向性任务")
    print("   - 成交量变化同向性任务")
    print("3. 注意力机制用于获取序列总表征")
    print("4. 锚-样本序列对采样策略")

    # 第二阶段：微调进行股票涨跌预测
    print("\n" + "=" * 50)
    print("第二阶段：微调进行股票涨跌预测")
    lstm_predictor = finetune_model(pretrained_model, scaler)

    print("\n" + "=" * 50)
    print("训练完成！")
    print("模型已完成两阶段训练：")
    print("✅ 阶段1：自监督预训练 - 学习股票序列的通用表征")
    print("✅ 阶段2：LSTM微调 - 进行股票涨跌预测")
    print("\n该实现完全符合专利CN113159945A的技术方案！")
