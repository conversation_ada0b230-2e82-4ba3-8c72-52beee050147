# 基于多任务自监督学习的股票涨跌预测方法

## 📋 项目简介

本项目完全按照专利 **CN113159945A** 实现了一种基于多任务自监督学习的股票涨跌预测方法。

### 🎯 核心特性

- ✅ **完全符合专利要求**：严格按照专利CN113159945A的技术方案实现
- ✅ **一键训练**：简单调用即可训练模型
- ✅ **一键预测**：加载模型即可进行股票涨跌预测
- ✅ **标准K线数据格式**：支持常见的K线数据格式

## 🚀 快速开始

### 安装依赖

```bash
pip install torch numpy scikit-learn
```

### 基本用法

```python
from stock_predictor_simple import train_stock_predictor, predict_stock_trend

# 1. 准备K线数据
kline_data = [
    [
        1499040000000,      # 开盘时间
        "0.01634790",       # 开盘价
        "0.80000000",       # 最高价
        "0.01575800",       # 最低价
        "0.01577100",       # 收盘价
        "148976.11427815",  # 成交量
        1499644799999,      # 收盘时间
        "2434.19055334",    # 成交额
        308,                # 成交笔数（可选）
        "1756.87402397",    # 主动买入成交量（可选）
        "28.46694368",      # 主动买入成交额（可选）
        "17928899.62484339" # 忽略参数（可选）
    ],
    # ... 更多K线数据
]

# 2. 一键训练模型
model, scaler = train_stock_predictor(
    kline_data_list=kline_data,
    k=5,                    # 序列长度（天数）
    epochs=10,              # 训练轮数
    model_path='my_model.pth'  # 模型保存路径
)

# 3. 一键预测
prediction_data = kline_data[-5:]  # 最近5天的数据
result = predict_stock_trend(
    kline_data=prediction_data,
    model_path='my_model.pth'
)

print(f"预测结果: {result}")
# 输出示例:
# {
#     'prediction': 1,           # 0=跌, 1=涨
#     'probability': 0.65,       # 上涨概率
#     'confidence': '中',        # 置信度
#     'confidence_score': 0.30   # 置信度分数
# }
```

## 📊 数据格式说明

### K线数据格式

本项目支持标准的K线数据格式，**自动提取专利要求的6维特征**：

| 位置 | 字段名 | 说明 | 是否必需 |
|------|--------|------|----------|
| 0 | 开盘时间 | 时间戳 | ❌ |
| 1 | 开盘价 | 开盘价格 | ✅ |
| 2 | 最高价 | 最高价格 | ✅ |
| 3 | 最低价 | 最低价格 | ✅ |
| 4 | 收盘价 | 收盘价格 | ✅ |
| 5 | 成交量 | 成交量 | ✅ |
| 6 | 收盘时间 | 时间戳 | ❌ |
| 7 | 成交额 | 成交金额 | ✅ |
| 8+ | 其他字段 | 可选字段 | ❌ |

**专利要求的6维特征**：`[最高价, 最低价, 开盘价, 收盘价, 成交量, 成交额]`

### 数据要求

- **最少数据量**：至少需要 `k+1` 天的K线数据（默认k=5，即6天）
- **数据格式**：支持字符串和数字格式的价格数据
- **数据完整性**：必需字段不能为空

## 🏗️ 技术架构

### 模型组件

1. **TransformerEncoder**：基于Transformer的序列编码器
2. **SelfSupervisedTasks**：三个自监督辅助任务
   - 正负样本判别任务
   - 价格变化同向性任务
   - 成交量变化同向性任务
3. **LSTMPredictor**：基于LSTM的股票涨跌预测器

### 训练流程

1. **数据预处理**：提取6维特征，标准化处理
2. **模型训练**：端到端训练，自动优化
3. **模型评估**：测试集评估，输出准确率
4. **模型保存**：保存模型权重和预处理器

## 📈 性能说明

- **模型参数**：约65万个参数
- **训练速度**：CPU上约1-2分钟（小数据集）
- **预测速度**：毫秒级响应
- **内存占用**：约100MB

## 🔧 高级用法

### 自定义参数

```python
# 训练时自定义参数
model, scaler = train_stock_predictor(
    kline_data_list=your_data,
    k=10,                   # 使用10天序列
    epochs=20,              # 训练20轮
    model_path='custom_model.pth'
)

# 预测时指定模型
result = predict_stock_trend(
    kline_data=prediction_data,
    model_path='custom_model.pth'
)
```

### 批量预测

```python
# 对多个股票进行预测
stocks_data = {
    'AAPL': aapl_kline_data,
    'TSLA': tsla_kline_data,
    'MSFT': msft_kline_data
}

predictions = {}
for symbol, data in stocks_data.items():
    result = predict_stock_trend(data[-5:])  # 最近5天
    predictions[symbol] = result
    print(f"{symbol}: {result['prediction']} ({result['probability']:.1%})")
```

## ⚠️ 注意事项

1. **数据质量**：确保K线数据的准确性和完整性
2. **模型局限**：预测结果仅供参考，不构成投资建议
3. **风险提示**：股市有风险，投资需谨慎
4. **数据隐私**：模型训练在本地进行，数据不会上传

## 📄 专利信息

- **专利号**：CN113159945A
- **专利名称**：一种基于多任务自监督学习的股票涨跌预测方法
- **实现状态**：✅ 完全符合专利技术方案

## 🤝 技术支持

如有技术问题，请检查：

1. **数据格式**：确保K线数据格式正确
2. **依赖版本**：确保PyTorch版本兼容
3. **内存空间**：确保有足够的内存和存储空间
4. **模型文件**：确保模型文件路径正确

## 📝 更新日志

- **v1.0.0**：完整实现专利CN113159945A技术方案
- 支持一键训练和预测
- 支持标准K线数据格式
- 完整的错误处理和用户提示

---

**免责声明**：本项目仅用于技术研究和学习目的，预测结果不构成任何投资建议。投资有风险，决策需谨慎。
